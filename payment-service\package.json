{"name": "payment-service", "version": "1.0.0", "description": "Payment processing service for Sniper Business Center", "main": "dist/server.js", "scripts": {"build": "shx rm -rf dist && tsc && shx cp -r src/views dist/views && shx cp -r public dist/", "start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "nanoid": "^3.3.4", "validator": "^13.11.0", "winston": "^3.17.0"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/validator": "^13.11.7", "autoprefixer": "^10.4.19", "nodemon": "^3.0.2", "postcss": "^8.4.39", "shx": "^0.4.0", "tailwindcss": "^3.4.6", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}