# =====================
# Main Domain: sniperbuisnesscenter.com
# =====================
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name sniperbuisnesscenter.com www.sniperbuisnesscenter.com;

    root /var/www/sniper;
    index index.html;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/sniperbuisnesscenter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sniperbuisnesscenter.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # ACME challenge for Certbot
    location /.well-known/acme-challenge/ {
        allow all;
        root /var/www/sniper;
    }

    # Backend API proxy
    location ^~ /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Main Flutter frontend
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Static assets
    location ~* \.(?:ico|css|js|gif|jpe?g|png|svg|woff|woff2|ttf|eot)$ {
        expires 1M;
        access_log off;
        add_header Cache-Control "public";
    }

    access_log /var/log/nginx/sniperbuisnesscenter.ssl.access.log;
    error_log /var/log/nginx/sniperbuisnesscenter.ssl.error.log;
}

# HTTP to HTTPS redirect for main domain
server {
    listen 80;
    listen [::]:80;
    server_name sniperbuisnesscenter.com www.sniperbuisnesscenter.com;
    location /.well-known/acme-challenge/ {
        allow all;
        root /var/www/sniper;
    }
    location / {
        return 301 https://$host$request_uri;
    }
}

# =====================
# Admin Frontend: admin.sniperbuisnesscenter.com
# =====================
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin.sniperbuisnesscenter.com;

    root /var/www/SBC-MS/admin-frontend-ms/dist/;
    index index.html;

    ssl_certificate /etc/letsencrypt/live/sniperbuisnesscenter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sniperbuisnesscenter.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        try_files $uri $uri/ /index.html;
    }

    access_log /var/log/nginx/admin.ssl.access.log;
    error_log /var/log/nginx/admin.ssl.error.log;
}

# HTTP to HTTPS redirect for admin frontend
server {
    listen 80;
    listen [::]:80;
    server_name admin.sniperbuisnesscenter.com;
    location / {
        return 301 https://$host$request_uri;
    }
}

# =====================
# Prims Proxy: prims.sniperbuisnesscenter.com
# =====================
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name prims.sniperbuisnesscenter.com;

    ssl_certificate /etc/letsencrypt/live/sniperbuisnesscenter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sniperbuisnesscenter.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    access_log /var/log/nginx/prims.ssl.access.log;
    error_log /var/log/nginx/prims.ssl.error.log;
}

# HTTP to HTTPS redirect for prims
server {
    listen 80;
    listen [::]:80;
    server_name prims.sniperbuisnesscenter.com;
    location / {
        return 301 https://$host$request_uri;
    }
}
