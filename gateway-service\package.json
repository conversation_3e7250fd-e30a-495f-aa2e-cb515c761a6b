{"name": "sbc-gateway-service", "version": "1.0.0", "description": "API Gateway for Sniper Business Center Microservices", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-http-proxy": "^1.6.3", "helmet": "^6.0.1", "http-proxy-middleware": "^2.0.6", "morgan": "^1.10.0", "winston": "^3.8.2"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/express-http-proxy": "^1.6.6", "@types/morgan": "^1.9.4", "@types/node": "^18.15.0", "nodemon": "^2.0.21", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}