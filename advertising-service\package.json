{"name": "advertising-service", "version": "1.0.0", "description": "Microservice for handling Advertisements and Ad Packs", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": ["microservice", "advertising", "ads"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.1", "nanoid": "^5.1.5", "pino": "^9.3.1", "pino-pretty": "^11.2.1"}, "devDependencies": {"@swc/core": "^1.11.18", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.14.10", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "nodemon": "^3.1.4", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}