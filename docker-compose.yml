# Specifies the version of the Docker Compose file format - THIS LINE WILL BE REMOVED

# Define global environment variables using an extension field
x-env-vars: &common-env-vars
  NODE_ENV: development
  USER_SERVICE_HOST: user-service
  USER_SERVICE_PORT: 3001
  USER_SERVICE_URL: http://user-service:3001
  NOTIFICATION_SERVICE_HOST: notification-service
  NOTIFICATION_SERVICE_PORT: 3002
  NOTIFICATION_SERVICE_URL: http://notification-service:3002
  PAYMENT_SERVICE_HOST: payment-service
  PAYMENT_SERVICE_PORT: 3003
  PAYMENT_SERVICE_URL: http://payment-service:3003
  PRODUCT_SERVICE_HOST: product-service
  PRODUCT_SERVICE_PORT: 3004
  PRODUCT_SERVICE_URL: http://product-service:3004
  ADVERTISING_SERVICE_HOST: advertising-service
  ADVERTISING_SERVICE_PORT: 3005
  ADVERTISING_SERVICE_URL: http://advertising-service:3005
  TOMBOLA_SERVICE_HOST: tombola-service
  TOMBOLA_SERVICE_PORT: 3006
  TOMBOLA_SERVICE_URL: http://tombola-service:3006
  SETTINGS_SERVICE_HOST: settings-service
  SETTINGS_SERVICE_PORT: 3007
  SETTINGS_SERVICE_URL: http://settings-service:3007
  ADMIN_FRONTEND_MS_HOST: admin-frontend-ms
  ADMIN_FRONTEND_MS_PORT: 80
  ADMIN_FRONTEND_MS_URL: http://admin-frontend-ms:80
  GLOBAL_MONGO_BASE_URI: "mongodb://host.docker.internal:27017" # REMOVED
  # Define common restart policy and healthcheck for Node.js services
x-node-service-defaults: &node-service-defaults
  restart: unless-stopped
  # HEALTHCHECK REMOVED FROM HERE - WILL BE ADDED PER SERVICE

services:
  nginx:
    image: nginx:alpine
    container_name: sbc_nginx_reverse_proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf.template:ro
      - ./frontend:/usr/share/nginx/flutter_app:ro
    environment:
      USER_SERVICE_HOST: user-service
      USER_SERVICE_PORT: 3001
      NOTIFICATION_SERVICE_HOST: notification-service
      NOTIFICATION_SERVICE_PORT: 3002
      PAYMENT_SERVICE_HOST: payment-service
      PAYMENT_SERVICE_PORT: 3003
      PRODUCT_SERVICE_HOST: product-service
      PRODUCT_SERVICE_PORT: 3004
      ADVERTISING_SERVICE_HOST: advertising-service
      ADVERTISING_SERVICE_PORT: 3005
      TOMBOLA_SERVICE_HOST: tombola-service
      TOMBOLA_SERVICE_PORT: 3006
      SETTINGS_SERVICE_HOST: settings-service
      SETTINGS_SERVICE_PORT: 3007
      ADMIN_FRONTEND_MS_HOST: admin-frontend-ms
      ADMIN_FRONTEND_MS_PORT: 80
    command: >
      /bin/sh -c "envsubst < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf && exec nginx -g 'daemon off;'"
    depends_on:
      user-service:
        condition: service_healthy
      payment-service:
        condition: service_healthy
      product-service:
        condition: service_healthy
      tombola-service:
        condition: service_healthy
      settings-service:
        condition: service_healthy
      notification-service:
        condition: service_healthy
      advertising-service:
        condition: service_healthy
      admin-frontend-ms:
        condition: service_started
    restart: unless-stopped

  user-service:
    <<: *node-service-defaults
    build:
      context: ./user-service
      target: development
    container_name: user_service # User changed this
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3001
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_user_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3001/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./user-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3001:3001"

  payment-service:
    <<: *node-service-defaults
    build:
      context: ./payment-service
      target: development
    container_name: payment_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3003
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_payment_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3003/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./payment-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3003:3003"

  product-service:
    <<: *node-service-defaults
    build:
      context: ./product-service
      target: development
    container_name: product_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3004
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_product_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3004/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./product-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3004:3004"

  tombola-service:
    <<: *node-service-defaults
    build:
      context: ./tombola-service
      target: development
    container_name: tombola_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3006
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_tombola_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3006/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./tombola-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3006:3006"

  settings-service:
    <<: *node-service-defaults
    build:
      context: ./settings-service
      target: development
    container_name: settings_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3007
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_settings_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3007/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./settings-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3007:3007"
    # REMOVED depends_on: - mongo (if it was here)

  notification-service:
    <<: *node-service-defaults
    build:
      context: ./notification-service
      target: development
    container_name: notification_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3002
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_notification_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3002/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./notification-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3002:3002"

  advertising-service:
    <<: *node-service-defaults
    build:
      context: ./advertising-service
      target: development
    container_name: advertising_service
    command: npm run dev
    environment:
      <<: *common-env-vars
      PORT: 3005
      MONGODB_URI_DEV: mongodb://host.docker.internal:27017/sbc_advertising_dev # Explicit URI
    healthcheck:
      # ADDED SPECIFIC HEALTHCHECK
      test: [ "CMD", "curl", "-f", "http://localhost:3005/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    volumes:
      - ./advertising-service:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3005:3005"

  admin-frontend-ms:
    restart: unless-stopped
    build:
      context: ./admin-frontend-ms
    container_name: admin_frontend
    environment:
      <<: *common-env-vars
    volumes:
      - ./admin-frontend-ms:/usr/src/app
      - /usr/src/app/node_modules
    working_dir: /usr/src/app
    ports:
      - "3030:80"

# mongo: (Commented out as we use host MongoDB)
#   image: mongo:latest
#   container_name: mongo-db
#   restart: unless-stopped
#   ports:
#     - "27017:27017"
#   volumes:
#     - mongo-data:/data/db
#   networks:
#     - app-network

networks:
  app-network:
    driver: bridge

# volumes: (mongo-data no longer needed)
#   mongo-data:
#     driver: local
