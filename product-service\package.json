{"name": "product-service", "version": "1.0.0", "description": "Product microservice for SBC marketplace", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@types/multer": "^1.4.12", "axios": "^1.6.0", "axois": "^0.0.1-security", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "winston": "^3.17.0"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}