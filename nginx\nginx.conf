worker_processes 1;

events {
    worker_connections 1024;
}

http {
    sendfile on;
    server_names_hash_bucket_size 64;

    # Define upstreams for all backend services pointing to environment variables
    upstream user_service {
        server ${USER_SERVICE_HOST}:${USER_SERVICE_PORT};
    }
    upstream notification_service {
        server ${NOTIFICATION_SERVICE_HOST}:${NOTIFICATION_SERVICE_PORT};
    }
    upstream payment_service {
        server ${PAYMENT_SERVICE_HOST}:${PAYMENT_SERVICE_PORT};
    }
    upstream product_service {
        server ${PRODUCT_SERVICE_HOST}:${PRODUCT_SERVICE_PORT};
    }
    upstream advertising_service {
        server ${ADVERTISING_SERVICE_HOST}:${ADVERTISING_SERVICE_PORT};
    }
    upstream tombola_service {
        server ${TOMBOLA_SERVICE_HOST}:${TOMBOLA_SERVICE_PORT};
    }
    upstream settings_service {
        server ${SETTINGS_SERVICE_HOST}:${SETTINGS_SERVICE_PORT};
    }
    upstream admin_frontend_ms {
        server ${ADMIN_FRONTEND_MS_HOST}:${ADMIN_FRONTEND_MS_PORT};
    }

    # Common proxy settings
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;

    # --- HTTPS Server for sniperbuisnesscenter.com ---
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name sniperbuisnesscenter.com www.sniperbuisnesscenter.com;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/sniperbuisnesscenter.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/sniperbuisnesscenter.com/privkey.pem;
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        # ACME challenge location (for renewals if needed by client)
        location /.well-known/acme-challenge/ {
            allow all;
            root /usr/share/nginx/flutter_app; # Should align with Flutter app root
        }

        # Logging
        access_log /var/log/nginx/sniperbuisnesscenter.ssl.access.log;
        error_log /var/log/nginx/sniperbuisnesscenter.ssl.error.log;

        # --- Static Asset Proxy for Payment Service ---
        location /css/ {
            proxy_pass http://payment_service/css/;
            # Common proxy headers are inherited or can be re-added if specific tuning is needed
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location /js/ {
            proxy_pass http://payment_service/js/;
            # Common proxy headers
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # --- API Locations ---
        location /api/users/ {
            proxy_pass http://user_service/api/users/;
        }
        location /api/partners/ { # Added from gateway
            proxy_pass http://user_service/api/partners/;
        }
        location /api/subscriptions/ { # Added from gateway
            proxy_pass http://user_service/api/subscriptions/;
        }
        location /api/withdrawals/ { # Added from gateway
            proxy_pass http://user_service/api/withdrawals/;
        }
        location /api/contacts/ { # Added from gateway
            proxy_pass http://user_service/api/contacts/;
        }
        location /api/notifications/ {
            proxy_pass http://notification_service/api/notifications/;
        }
        location /api/payments/ {
            proxy_pass http://payment_service/api/payments/;
        }
        location /api/transactions/ { # Added from gateway
            proxy_pass http://payment_service/api/transactions/;
        }
        location /api/products/ {
            proxy_pass http://product_service/api/products/;
        }
        location /api/flash-sales/ { # Added from gateway
            proxy_pass http://product_service/api/flash-sales/;
        }
        location /api/advertising/ {
            proxy_pass http://advertising_service/api/advertising/;
        }
        location /api/tombolas/ { # Path changed to plural based on gateway-service
            proxy_pass http://tombola_service/api/tombolas/;
        }
        location /api/settings/ {
            proxy_pass http://settings_service/api/settings/;
        }
        location /api/events/ { # Added from gateway
            proxy_pass http://settings_service/api/events/;
        }

        # Location for Flutter Web App
        location / {
            root /usr/share/nginx/flutter_app;
            try_files $uri $uri/ /index.html;
        }

        # Health check endpoint
        location /nginx_health {
            return 200 'Nginx for sniperbuisnesscenter.com (HTTPS) is healthy';
            add_header Content-Type text/plain;
        }

        # Inside your server block(s)
        location ~* \.(?:ico|css|js|gif|jpe?g|png|svg|woff|woff2|ttf|eot)$ {
            expires 1M;
            access_log off;
            add_header Cache-Control "public";
            # For the main flutter app, if assets are under /usr/share/nginx/flutter_app
            root /usr/share/nginx/flutter_app; # Adjust if assets are elsewhere for admin
        }
    }

    # --- HTTPS Server for admin.sniperbuisnesscenter.com ---
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name admin.sniperbuisnesscenter.com;

        # SSL Configuration (assuming same cert covers the subdomain, or adjust path if different)
        ssl_certificate /etc/letsencrypt/live/sniperbuisnesscenter.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/sniperbuisnesscenter.com/privkey.pem;
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        # ACME challenge location
        location /.well-known/acme-challenge/ {
            allow all;
            root /usr/share/nginx/html; # Generic root, or specific if admin_frontend_ms serves it
        }

        # Logging
        access_log /var/log/nginx/admin.sniperbuisnesscenter.ssl.access.log;
        error_log /var/log/nginx/admin.sniperbuisnesscenter.ssl.error.log;

        location / {
            proxy_pass http://admin_frontend_ms;
        }

        # Health check endpoint
        location /nginx_health_admin {
            return 200 'Nginx for admin.sniperbuisnesscenter.com (HTTPS) is healthy';
            add_header Content-Type text/plain;
        }
    }

    # --- HTTP to HTTPS Redirect for sniperbuisnesscenter.com ---
    server {
        listen 80;
        listen [::]:80;
        server_name sniperbuisnesscenter.com www.sniperbuisnesscenter.com;

        location /.well-known/acme-challenge/ {
            allow all;
            root /usr/share/nginx/flutter_app; # Align with where certbot expects to write challenges
        }
        location /nginx_health { # Keep health check accessible on HTTP for initial checks
            return 200 'Nginx for sniperbuisnesscenter.com (HTTP) is healthy - redirecting to HTTPS';
            add_header Content-Type text/plain;
        }
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # --- HTTP to HTTPS Redirect for admin.sniperbuisnesscenter.com ---
    server {
        listen 80;
        listen [::]:80;
        server_name admin.sniperbuisnesscenter.com;

        location /.well-known/acme-challenge/ {
            allow all;
            root /usr/share/nginx/html; # Generic root for challenges
        }
         location /nginx_health_admin { # Keep health check accessible on HTTP
            return 200 'Nginx for admin.sniperbuisnesscenter.com (HTTP) is healthy - redirecting to HTTPS';
            add_header Content-Type text/plain;
        }
        location / {
            return 301 https://$host$request_uri;
        }
    }
}

