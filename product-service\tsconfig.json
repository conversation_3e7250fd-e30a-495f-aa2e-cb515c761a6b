{"compilerOptions": {"target": "es2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "declaration": true, "declarationMap": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "ts-node": {"swc": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}