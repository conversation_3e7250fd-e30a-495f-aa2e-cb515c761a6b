{"name": "settings-service", "version": "1.0.0", "description": "Microservice for managing application settings", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["microservice", "settings", "config"], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.592.0", "axios": "^1.7.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "googleapis": "^137.1.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "mongoose": "^8.4.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "winston": "^3.13.0"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.57.0", "nodemon": "^3.1.3", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}