# User Service Environment Variables

# Server configuration
PORT=3001
NODE_ENV=development # set to 'production' in production

# MongoDB Connection URIs
MONGODB_URI_DEV=mongodb://localhost:27017/sbc_user_dev
MONGODB_URI_PROD=your_production_mongodb_uri_here

# JWT Configuration
JWT_SECRET=your_very_strong_jwt_secret # CHANGE THIS IN PRODUCTION
JWT_EXPIRATION=1d # Example: 1 day expiration

# Service URLs
SERVICE_SECRET=__REPLACE_WITH_STRONG_RANDOM_SECRET__
PAYMENT_SERVICE_URL=http://localhost:3003/api
NOTIFICATION_SERVICE_URL=http://localhost:3002/api
# Add other service URLs if needed (e.g., PRODUCT_SERVICE_URL)

# Self URL (for callbacks, etc.)
SELF_BASE_URL=http://localhost:3001 # URL where this service is reachable by others (e.g., payment service)

# Add other service-specific variables as needed
# e.g., API Keys for external services 