@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply bg-gray-900 text-gray-100;
  overflow: hidden;
  /* Prevent body from scrolling */
}

/*Toggle switch*/
.toggle-container {
  --active-color: rgba(69, 234, 124, 0.2);
  --inactive-color: #9ca3af6c;
  position: relative;
  aspect-ratio: 292 / 142;
  height: 1.875em;
}

.toggle-input {
  appearance: none;
  margin: 0;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.toggle {
  width: 100%;
  height: 100%;
  overflow: visible;
}

.toggle-background {
  fill: var(--inactive-color);
  transition: fill .4s;
}

.toggle-input:checked+.toggle .toggle-background {
  fill: var(--active-color);
}

.toggle-circle-center {
  transform-origin: center;
  transition: transform .6s;
}

.toggle-input:checked+.toggle .toggle-circle-center {
  transform: translateX(150px);
}

.toggle-circle {
  transform-origin: center;
  transition: transform .45s;
  backface-visibility: hidden;
}

.toggle-circle.left {
  transform: scale(1);
}

.toggle-input:checked+.toggle .toggle-circle.left {
  transform: scale(0);
}

.toggle-circle.right {
  transform: scale(0);
}

.toggle-input:checked+.toggle .toggle-circle.right {
  transform: scale(1);
}

.toggle-icon {
  transition: fill .4s;
}

.toggle-icon.on {
  fill: var(--inactive-color);
}

.toggle-input:checked+.toggle .toggle-icon.on {
  fill: #fff;
}

.toggle-icon.off {
  fill: #eaeaec;
}

.toggle-input:checked+.toggle .toggle-icon.off {
  fill: var(--active-color);
}

/*Login Page background*/
.background {
  background: url('./assets/bg-login.jpg');
  background-position: center;
  background-size: cover;
  animation: moveBackground 50s linear infinite;
}

@keyframes moveBackground {
  0% {
    background-position: left center;
  }

  50% {
    background-position: right center;
  }

  100% {
    background-position: left center;
  }
}

/*Loader*/
.container {
  width: 150px;
  height: 10px;
  border: 2px solid #b2b2b2;
  border-radius: 7px;
  margin: 0 auto;
  padding: 2px 1px;
  overflow: hidden;
  font-size: 0;
}

.box {
  width: 9px;
  height: 100%;
  background: linear-gradient(to bottom, #2838c7 0%, #5979ef 17%, #869ef3 32%, #869ef3 45%, #5979ef 59%, #2838c7 100%);
  display: inline-block;
  margin-right: 2px;
  animation: loader 2s infinite;
  animation-timing-function: linear;
}

.logo {
  width: 220px;
  margin: 50px auto;
  margin-top: 20px;
}

.logo p {
  margin: 0;
  padding: 0;
}

.top {
  font-size: 16px;
  font-weight: 300;
  line-height: 16px;
}

.top:after {
  content: "\00a9";
  font-size: 10px;
  position: relative;
  top: -5px;
  margin-left: 2px;
}

.mid {
  font-size: 46px;
  font-weight: 700;
  line-height: 36px;
}

.mid span {
  font-size: 22px;
  display: inline-block;
  vertical-align: top;
  color: #FF6821;
  margin-top: -8px;
}

.logo .bottom {
  font-size: 30px;
  font-weight: 300;
  line-height: 30px;
  margin-left: 5px;
}

@keyframes loader {
  0% {
    transform: translate(-30px);
  }

  100% {
    transform: translate(150px);
  }
}