# Node Environment
NODE_ENV=development
PORT=3003
HOST=0.0.0.0

# MongoDB Database
MONGODB_URI=mongodb://localhost:27017/sbc_payment_dev

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=1h

# Service URLs
USER_SERVICE_URL=http://localhost:3001/api
NOTIFICATION_SERVICE_URL=http://localhost:3002/api
SERVICE_SECRET=sbc_all_services

# Frontend URL (for redirects after payment)
FRONTEND_URL=http://localhost:3000
PAYMENT_SERVICE_BASE_URL=http://localhost:3003

# Log Level
LOG_LEVEL=info

# FeexPay Configuration (Countries: Benin, Côte d'Ivoire, Senegal, Congo Brazzaville, Togo)
FEEXPAY_API_KEY=your_feexpay_api_key
FEEXPAY_SHOP_ID=your_feexpay_shop_id
FEEXPAY_BASE_URL=https://api.feexpay.me/api
FEEXPAY_WEBHOOK_SECRET=your_feexpay_webhook_secret

# CinetPay Configuration (Countries: Cameroon, Burkina Faso, Guinea Conakry, Mali, Niger)
CINETPAY_API_KEY=your_cinetpay_api_key
CINETPAY_SITE_ID=your_cinetpay_site_id
CINETPAY_BASE_URL=https://api-checkout.cinetpay.com/v2
CINETPAY_NOTIFICATION_KEY=your_cinetpay_notification_key
CINETPAY_ALTERNATE_NOTIFY_URL=https://your-domain.com/api/payments/webhooks/cinetpay

# Payment Gateway Configuration
PAYMENT_GATEWAY_API_KEY=your_payment_gateway_api_key
PAYMENT_GATEWAY_SECRET_KEY=your_payment_gateway_secret_key
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
LYGOS_API_KEY=your_lygos_api_key
LYGOS_BASE_URL=https://api.lygosapp.com/v1
LYGOS_SHOP_NAME=SBC
LYGOS_WEBHOOK_SECRET=your_lygos_webhook_secret

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost
RABBITMQ_EXCHANGE=sbc_events
RABBITMQ_QUEUE_PAYMENT=payment_queue