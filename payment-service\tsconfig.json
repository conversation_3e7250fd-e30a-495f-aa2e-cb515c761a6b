{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "declaration": true, "sourceMap": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "ts-node": {"swc": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}