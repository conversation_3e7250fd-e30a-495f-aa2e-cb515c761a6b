{"compilerOptions": {"target": "ES2016", "module": "CommonJS", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "ts-node": {"swc": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}