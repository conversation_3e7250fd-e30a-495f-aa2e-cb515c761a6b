# Application Settings
NODE_ENV=development
PORT=3007

# Database Settings
MONGODB_URI=mongodb://localhost:27017/settingsDB_dev

# JWT Settings
JWT_SECRET=yoursecretkeycomeshere
ACCESS_TOKEN_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=7d

# Google Drive Credentials (Service Account)
# Create a Service Account in Google Cloud Console and download the JSON key.
# Ensure this service account has edit access to the target Drive folder/files.
DRIVE_CLIENT_EMAIL=<EMAIL>
# The private key from the JSON file. Enclose in quotes and ensure newlines are escaped (\\n).
DRIVE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_CONTENT_WITH_ESCAPED_NEWLINES\\n-----END PRIVATE KEY-----\\n"
# Optional: Specify the ID of the Google Drive folder where settings files should be stored.
DRIVE_SETTINGS_FOLDER_ID=YOUR_GOOGLE_DRIVE_FOLDER_ID

# CORS Allowed Origins (comma-separated, or * for all)
# Example: ALLOWED_ORIGINS=http://localhost:5173,https://your-admin-frontend.com
ALLOWED_ORIGINS=*

# Logging Configuration
LOG_LEVEL=info # Options: error, warn, info, http, verbose, debug, silly
LOG_DIR=./logs