# Tombola Service Environment Variables

# Server configuration
PORT=3005
NODE_ENV=development # set to 'production' in production
HOST=0.0.0.0

# MongoDB Connection URIs
MONGODB_URI_DEV=mongodb://localhost:27017/sbc_advertising_dev
MONGODB_URI_PROD=your_production_mongodb_uri_here

# JWT Configuration
JWT_SECRET=sbc$jwt$secret
JWT_EXPIRATION=1d # Example: 1 day expiration


# Service URLs
SERVICE_SECRET=__REPLACE_WITH_STRONG_RANDOM_SECRET__
USER_SERVICE_URL=http://localhost:3001/api
PAYMENT_SERVICE_URL=http://localhost:3003/api
NOTIFICATION_SERVICE_URL=http://localhost:3002/api
PRODUCT_SERVICE_URL=http://localhost:3004/api
API_GATEWAY_URL=http://localhost:3000/api

# Self URL (for callbacks, etc.)
SELF_BASE_URL=http://localhost:3005 # URL where this service is reachable by others

BODY_LIMIT=10mb
CORS_ORIGIN=*

# Withdrawal Configuration
DAILY_WITHDRAWAL_LIMIT=50000
MAX_WITHDRAWALS_PER_DAY=3

# Logging
LOG_LEVEL=info

# Add other service-specific variables as needed
# e.g., API Keys for external services 