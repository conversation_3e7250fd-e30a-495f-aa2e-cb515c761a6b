{"name": "user-service", "version": "1.0.0", "description": "User management service for Sniper Business Center", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nanoid": "^5.1.5", "uuid": "^11.1.0", "validator": "^13.11.0", "winston": "^3.17.0"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.10.4", "@types/validator": "^13.11.7", "nodemon": "^3.1.4", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}