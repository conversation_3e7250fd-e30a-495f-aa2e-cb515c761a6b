{"name": "notification-service", "version": "1.0.0", "description": "Notification microservice for SBC platform", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node --swc src/server.ts", "start:worker:broadcast": "node dist/workers/broadcast.worker.js", "dev:worker:broadcast": "nodemon --exec ts-node --swc src/workers/broadcast.worker.ts", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "dependencies": {"@types/amqplib": "^0.10.7", "amqplib": "^0.10.7", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "winston": "^3.17.0"}, "devDependencies": {"@swc/core": "^1.11.18", "@swc/register": "^0.1.10", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/jest": "^29.5.6", "@types/jsonwebtoken": "^9.0.4", "@types/morgan": "^1.9.7", "@types/node": "^20.8.9", "@types/nodemailer": "^6.4.13", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}