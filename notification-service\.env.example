# Server settings
PORT=3002
NODE_ENV=development

# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/sbc_notification_dev

# JWT Secret (must match across services for authentication)
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=1d

# Email Configuration (for <PERSON><PERSON>mail<PERSON>)
EMAIL_SERVICE=smtp.example.com
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=Sniper Business Center <<EMAIL>>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# RabbitMQ Configuration (for message queue)
RABBITMQ_URL=amqp://localhost
RABBITMQ_NOTIFICATION_QUEUE=notification_queue

# User Service URL (for inter-service communication)
USER_SERVICE_URL=http://localhost:3001/api 