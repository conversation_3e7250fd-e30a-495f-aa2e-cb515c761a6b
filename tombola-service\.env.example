# Server Configuration
NODE_ENV=development
PORT=3004 # Assign a unique port for this service

# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/tombola_db # Use a dedicated DB

# Service URLs (adjust ports as needed)
PAYMENT_SERVICE_URL=http://localhost:3003/api
NOTIFICATION_SERVICE_URL=http://localhost:3002/api
PRODUCT_SERVICE_URL=http://localhost:3004/api
API_GATEWAY_URL=http://localhost:3000/api
USER_SERVICE_URL=http://localhost:3001/api

# JWT Secret (if needed for service-to-service auth)
# JWT_SECRET=your_very_secret_key_tombola

# API Keys (if needed for inter-service auth)
# SERVICE_API_KEY=your_secure_api_key_for_tombola

# Tombola Configuration
TOMBOLA_TICKET_PRICE=200 # Price in XAF 

# Self URL (for callbacks, etc.)
SELF_BASE_URL=http://localhost:3006 # URL where this service is reachable by others 