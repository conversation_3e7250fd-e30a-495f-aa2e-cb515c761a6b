{"name": "admin_dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.7", "canvas-confetti": "^1.9.3", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "lucide-react": "^0.436.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.13.0", "react-router-dom": "^6.26.1", "recharts": "^2.12.7", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.16", "@types/node": "^22.5.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}