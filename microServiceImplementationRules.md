## General Guide: TypeScript Microservices Structure

This section provides a general template and guidelines for structuring a microservices project using Node.js and TypeScript, similar to the patterns used in this example. This approach leverages TypeScript's static typing and modern tooling for better maintainability and scalability.

**Core Principles:**

*   **Separate Services:** Each microservice resides in its own dedicated directory (e.g., `service-a/`, `service-b/`).
*   **Containerization:** Each service (and infrastructure component) is containerized using Docker (`Dockerfile` per service).
*   **Orchestration:** `docker-compose.yml` is used at the root level to define, configure, and link all services and infrastructure components (databases, message queues, etc.) for local development and testing.
*   **API Gateway:** An API Gateway (often Nginx or a dedicated Node.js service) acts as the single entry point for external requests, routing them to the appropriate internal service.

**Steps for Setting Up Each Node.js Microservice in TypeScript:**

1.  **Directory Structure (Example: `service-a/`):**
    *   `service-a/`
        *   `src/` - Contains all TypeScript source code (`.ts` files).
            *   `index.ts` - Main entry point for the service.
            *   `config/` - Configuration loading (e.g., environment variables).
            *   `api/` - API route definitions, controllers.
            *   `services/` - Business logic.
            *   `database/` - Database connection, models, repositories (if applicable).
            *   `utils/` - Shared utilities within the service.
            *   `types/` - Custom type definitions (`*.d.ts`).
        *   `Dockerfile` - Instructions to build the service's Docker image.
        *   `package.json` - Project dependencies and scripts.
        *   `tsconfig.json` - TypeScript compiler configuration.
        *   `.env.example` - Example environment variables file.
        *   `.dockerignore` - Specifies files/folders to ignore during Docker build.

2.  **TypeScript Setup (`tsconfig.json` per service):**
    *   Create a `tsconfig.json` in each service's root.
    *   Configure compiler options (target, module system, output directory, strictness, etc.). A common setup:
        ```json
        {
          "compilerOptions": {
            "target": "ES2016", // Or newer
            "module": "CommonJS",
            "outDir": "./dist",
            "rootDir": "./src",
            "strict": true,
            "esModuleInterop": true,
            "skipLibCheck": true,
            "forceConsistentCasingInFileNames": true,
            "resolveJsonModule": true
          },
          "include": ["src/**/*"],
          "exclude": ["node_modules", "**/*.test.ts"]
        }
        ```

3.  **Dependencies (`package.json` per service):**
    *   **Core:** `typescript`, `@types/node`, `ts-node` (or `ts-node-dev`), a web framework (`express`, `fastify`, etc.) and its types (`@types/express`).
    *   **Infrastructure Clients:** Types and clients for databases (`pg`, `mongoose`, `@types/mongoose`), message queues (`amqplib`, `@types/amqplib`, `kafkajs`), etc., as needed by the service.
    *   **Utilities:** `@types/cors`, `dotenv`, etc.

4.  **Scripts (`package.json` per service):**
    *   `"build": "tsc"` - Compiles TypeScript to JavaScript (`dist` folder).
    *   `"start": "NODE_ENV=production node dist/index.js"` - Runs the compiled JS for production.
    *   `"dev": "NODE_ENV=development ts-node-dev --respawn --transpile-only src/index.ts"` - Runs the service in development with live reloading.
    *   `"test": "jest"` - (Or your preferred test runner).

5.  **Dockerfile (Multi-Stage Build per service):**
    *   Use a multi-stage build to keep the final image small and secure.
    *   **Build Stage:** Use a full Node image, install *all* dependencies (including dev), copy source code, compile TypeScript (`npm run build`).
    *   **Production Stage:** Use a slim Node image (e.g., `node:alpine`), install *only* production dependencies (`npm ci --omit=dev`), copy compiled code (`dist` folder) and `node_modules` from the build stage, expose the necessary port, and set the `CMD` to run the application (`node dist/index.js`).
    *   Use `.dockerignore` effectively.

6.  **Orchestration (`docker-compose.yml` at root):**
    *   Define services for each microservice, pointing to their respective `Dockerfile` and context directory.
    *   Define services for infrastructure (databases like Postgres/Mongo, message brokers like RabbitMQ/Kafka, caching like Redis).
    *   Define the API Gateway/Reverse Proxy (e.g., Nginx pointing to its config file, or another Node.js service).
    *   Configure ports, volumes (for data persistence), environment variables (using `.env` files or direct definition), networks, and `depends_on` relationships.

**Common Infrastructure Components:**

*   **Database:** Choose based on needs (SQL: Postgres, MySQL; NoSQL: MongoDB, Cassandra). Define as a service in `docker-compose.yml`.
*   **Message Queue:** For asynchronous communication (RabbitMQ, Kafka, NATS). Define as a service in `docker-compose.yml`.
*   **API Gateway / Reverse Proxy:** Handles incoming requests, routing, load balancing, SSL termination (Nginx, Traefik, or a custom Node.js gateway). Define as a service in `docker-compose.yml`.

This generalized structure provides a solid foundation for building scalable and maintainable microservices applications using Node.js and TypeScript. Remember to adapt the specific tools and configurations based on your project's requirements.
